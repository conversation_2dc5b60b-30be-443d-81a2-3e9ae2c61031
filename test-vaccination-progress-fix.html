<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح شريط التقدم للتلقيحات</title>
    <style>
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --success-color: #10b981;
            --info-color: #3b82f6;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --radius-lg: 0.75rem;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            margin: 0;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--bg-primary);
            padding: 30px;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
        }

        h1 {
            text-align: center;
            color: var(--info-color);
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: var(--bg-tertiary);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-color);
        }

        .child-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-sm);
        }

        .child-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            border-color: var(--info-color);
        }

        .child-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .child-birth-date {
            color: var(--text-secondary);
            margin-bottom: 10px;
        }

        /* Vaccination progress styles */
        .vaccination-progress-container {
            background: var(--bg-tertiary);
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            border: 1px solid var(--border-color);
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .progress-count {
            font-weight: bold;
            color: var(--info-color);
        }

        .progress-bar-container {
            background: var(--bg-secondary);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .progress-bar {
            background: var(--success-color);
            height: 100%;
            transition: width 0.3s ease;
        }

        .progress-percentage {
            text-align: center;
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-top: 5px;
        }

        .controls {
            text-align: center;
            margin-top: 20px;
        }

        .btn {
            background: var(--info-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-success {
            background: var(--success-color);
        }

        .status {
            text-align: center;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }

        .status.success {
            background: #dcfce7;
            color: #16a34a;
            border: 1px solid #86efac;
        }

        .status.info {
            background: #dbeafe;
            color: #2563eb;
            border: 1px solid #93c5fd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار إصلاح شريط التقدم للتلقيحات</h1>
        
        <div class="test-section">
            <h2>📋 بيانات الطفل التجريبية</h2>
            <div id="childCard" class="child-card">
                <div class="child-name">أحمد محمد</div>
                <div class="child-birth-date">📅 2023-01-15</div>
                <div class="vaccination-progress-container">
                    <div class="progress-info">
                        <span>التلقيحات المنجزة:</span>
                        <span class="progress-count" id="progressCount">3/12</span>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar" id="progressBar" style="width: 25%"></div>
                    </div>
                    <div class="progress-percentage" id="progressPercentage">25%</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🎮 أدوات التحكم</h2>
            <div class="controls">
                <button class="btn btn-success" onclick="addVaccination()">➕ إضافة تلقيح</button>
                <button class="btn" onclick="removeVaccination()">➖ إزالة تلقيح</button>
                <button class="btn" onclick="resetProgress()">🔄 إعادة تعيين</button>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 حالة الاختبار</h2>
            <div id="status" class="status info">
                جاهز للاختبار - انقر على الأزرار أعلاه لاختبار تحديث شريط التقدم
            </div>
        </div>

        <div class="test-section">
            <h2>✅ نتائج الاختبار</h2>
            <ul id="testResults">
                <li>✅ تم تحميل شريط التقدم بنجاح</li>
                <li>⏳ في انتظار اختبار التحديث...</li>
            </ul>
        </div>
    </div>

    <script>
        let completedVaccinations = 3;
        const totalVaccinations = 12;
        let testCount = 0;

        function updateProgressBar() {
            const progressPercent = Math.round((completedVaccinations / totalVaccinations) * 100);
            
            // تحديث العناصر
            document.getElementById('progressCount').textContent = `${completedVaccinations}/${totalVaccinations}`;
            document.getElementById('progressBar').style.width = `${progressPercent}%`;
            document.getElementById('progressPercentage').textContent = `${progressPercent}%`;
            
            // تحديث الحالة
            const status = document.getElementById('status');
            status.className = 'status success';
            status.textContent = `✅ تم تحديث شريط التقدم: ${completedVaccinations}/${totalVaccinations} (${progressPercent}%)`;
            
            // إضافة نتيجة الاختبار
            const results = document.getElementById('testResults');
            testCount++;
            const li = document.createElement('li');
            li.textContent = `✅ اختبار ${testCount}: تحديث شريط التقدم إلى ${progressPercent}% - نجح`;
            results.appendChild(li);
            
            console.log('تم تحديث شريط التقدم:', {
                completed: completedVaccinations,
                total: totalVaccinations,
                percentage: progressPercent
            });
        }

        function addVaccination() {
            if (completedVaccinations < totalVaccinations) {
                completedVaccinations++;
                updateProgressBar();
            } else {
                const status = document.getElementById('status');
                status.className = 'status info';
                status.textContent = 'ℹ️ تم إكمال جميع التلقيحات بالفعل';
            }
        }

        function removeVaccination() {
            if (completedVaccinations > 0) {
                completedVaccinations--;
                updateProgressBar();
            } else {
                const status = document.getElementById('status');
                status.className = 'status info';
                status.textContent = 'ℹ️ لا توجد تلقيحات لإزالتها';
            }
        }

        function resetProgress() {
            completedVaccinations = 0;
            updateProgressBar();
            
            const status = document.getElementById('status');
            status.className = 'status info';
            status.textContent = '🔄 تم إعادة تعيين التقدم';
        }

        // اختبار تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 بدء اختبار شريط التقدم للتلقيحات');
            
            // اختبار تلقائي بعد ثانيتين
            setTimeout(() => {
                console.log('🔄 تشغيل اختبار تلقائي...');
                addVaccination();
                
                setTimeout(() => {
                    addVaccination();
                    
                    setTimeout(() => {
                        removeVaccination();
                        
                        const results = document.getElementById('testResults');
                        const li = document.createElement('li');
                        li.textContent = '✅ اكتمل الاختبار التلقائي بنجاح';
                        li.style.color = 'var(--success-color)';
                        li.style.fontWeight = 'bold';
                        results.appendChild(li);
                    }, 1000);
                }, 1000);
            }, 2000);
        });
    </script>
</body>
</html>
